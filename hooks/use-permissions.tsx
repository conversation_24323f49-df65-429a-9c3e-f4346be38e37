'use client';

import { useState, useEffect } from 'react';
import { Permission } from '@/types/auth';

interface PermissionState {
  permissions: Permission[];
  roles: string[];
  loading: boolean;
  error: string | null;
}

/**
 * Client-side hook for checking user permissions and roles
 * Fetches current user's permissions from session API
 */
export function usePermissions() {
  const [state, setState] = useState<PermissionState>({
    permissions: [],
    roles: [],
    loading: true,
    error: null,
  });

  useEffect(() => {
    fetchPermissions();
  }, []);

  const fetchPermissions = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const response = await fetch('/api/auth/permissions');
      if (!response.ok) {
        throw new Error('Failed to fetch permissions');
      }
      
      const data = await response.json();
      setState({
        permissions: data.permissions || [],
        roles: data.roles || [],
        loading: false,
        error: null,
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }));
    }
  };

  const hasPermission = (permission: Permission): boolean => {
    return state.permissions.includes(permission) || state.permissions.includes(Permission.AdminAccess);
  };

  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  const hasRole = (role: string): boolean => {
    return state.roles.includes(role);
  };

  const isAdmin = (): boolean => {
    return hasPermission(Permission.AdminAccess);
  };

  const refreshPermissions = () => {
    fetchPermissions();
  };

  return {
    ...state,
    hasPermission,
    hasAnyPermission,
    hasRole,
    isAdmin,
    refreshPermissions,
  };
}

/**
 * Higher-order component for permission-based rendering
 */
interface PermissionGuardProps {
  children: React.ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  role?: string;
  fallback?: React.ReactNode;
  requireAll?: boolean;
}

export function PermissionGuard({
  children,
  permission,
  permissions,
  role,
  fallback = null,
  requireAll = false,
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasRole, loading } = usePermissions();

  if (loading) {
    return <div className="animate-pulse bg-gray-200 h-4 w-full rounded" />;
  }

  let hasAccess = true;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (permissions) {
    hasAccess = requireAll 
      ? permissions.every(p => hasPermission(p))
      : hasAnyPermission(permissions);
  } else if (role) {
    hasAccess = hasRole(role);
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
} 
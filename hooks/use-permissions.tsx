'use client';

import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Permission } from '@/types/auth';

interface PermissionResponse {
  success: boolean;
  permissions: Permission[];
  roles: string[];
  timestamp: string;
  error?: string;
}

interface RefreshRolesResponse {
  success: boolean;
  message: string;
  data: {
    email: string;
    roles: string[];
    timestamp: string;
  };
}

// Query keys for TanStack Query
export const permissionKeys = {
  all: ['permissions'] as const,
  current: () => [...permissionKeys.all, 'current'] as const,
  refresh: () => [...permissionKeys.all, 'refresh'] as const,
};

/**
 * Fetch current user's permissions and roles from API
 */
async function fetchPermissions(): Promise<PermissionResponse> {
  const response = await fetch('/api/auth/permissions');

  if (!response.ok) {
    throw new Error(`Failed to fetch permissions: ${response.status}`);
  }

  return response.json();
}

/**
 * Refresh user roles from external QBraid API
 */
async function refreshRoles(): Promise<RefreshRolesResponse> {
  const response = await fetch('/api/refresh-roles', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
  });

  if (!response.ok) {
    throw new Error(`Failed to refresh roles: ${response.status}`);
  }

  return response.json();
}

/**
 * Enhanced permissions hook using TanStack Query
 * Provides automatic caching, background updates, and optimistic updates
 */
export function usePermissions() {
  const queryClient = useQueryClient();

  // Main permissions query with automatic caching and background updates
  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: permissionKeys.current(),
    queryFn: fetchPermissions,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: true, // Refetch when user returns to tab
    refetchOnMount: true,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Mutation for refreshing roles from external API
  const refreshRolesMutation = useMutation({
    mutationFn: refreshRoles,
    onSuccess: (data) => {
      // Invalidate and refetch permissions after successful role refresh
      queryClient.invalidateQueries({ queryKey: permissionKeys.current() });

      // Optionally show success notification
      console.log('✅ [PERMISSIONS] Roles refreshed:', data.data.roles);
    },
    onError: (error) => {
      console.error('❌ [PERMISSIONS] Failed to refresh roles:', error);
    },
  });

  // Extract permissions and roles from data
  const permissions = data?.permissions || [];
  const roles = data?.roles || [];

  // Permission checking utilities
  const hasPermission = (permission: Permission): boolean => {
    return permissions.includes(permission) || permissions.includes(Permission.AdminAccess);
  };

  const hasAnyPermission = (requiredPermissions: Permission[]): boolean => {
    return requiredPermissions.some((permission) => hasPermission(permission));
  };

  const hasAllPermissions = (requiredPermissions: Permission[]): boolean => {
    return requiredPermissions.every((permission) => hasPermission(permission));
  };

  const hasRole = (role: string): boolean => {
    return roles.includes(role);
  };

  const hasAnyRole = (requiredRoles: string[]): boolean => {
    return requiredRoles.some((role) => hasRole(role));
  };

  const isAdmin = (): boolean => {
    return hasPermission(Permission.AdminAccess);
  };

  // Refresh functions
  const refreshPermissions = () => {
    refetch();
  };

  const refreshRolesFromAPI = () => {
    refreshRolesMutation.mutate();
  };

  return {
    // Data
    permissions,
    roles,

    // Loading states
    loading: isLoading,
    isLoading,
    isRefreshing: refreshRolesMutation.isPending,

    // Error states
    error: error?.message || null,
    isError,
    refreshError: refreshRolesMutation.error?.message || null,

    // Permission checking utilities
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    isAdmin,

    // Actions
    refreshPermissions,
    refreshRolesFromAPI,

    // Raw query data for advanced usage
    queryData: data,
    isStale: data ? Date.now() - new Date(data.timestamp).getTime() > 5 * 60 * 1000 : false,
  };
}

/**
 * Higher-order component for permission-based rendering with TanStack Query integration
 */
interface PermissionGuardProps {
  children: React.ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  role?: string;
  roles?: string[];
  fallback?: React.ReactNode;
  requireAll?: boolean;
  showLoadingState?: boolean;
}

export function PermissionGuard({
  children,
  permission,
  permissions,
  role,
  roles,
  fallback = null,
  requireAll = false,
  showLoadingState = true,
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, hasRole, hasAnyRole, loading } =
    usePermissions();

  // Show loading state if enabled and data is loading
  if (loading && showLoadingState) {
    return <div className="animate-pulse bg-gray-200 h-4 w-full rounded" />;
  }

  let hasAccess = true;

  // Check permissions
  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (permissions) {
    hasAccess = requireAll ? hasAllPermissions(permissions) : hasAnyPermission(permissions);
  }

  // Check roles
  if (hasAccess && role) {
    hasAccess = hasRole(role);
  } else if (hasAccess && roles) {
    hasAccess = requireAll ? roles.every((r) => hasRole(r)) : hasAnyRole(roles);
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

/**
 * Hook for invalidating permissions cache
 * Useful for components that modify user roles/permissions
 */
export function useInvalidatePermissions() {
  const queryClient = useQueryClient();

  return {
    invalidatePermissions: () => {
      queryClient.invalidateQueries({ queryKey: permissionKeys.current() });
    },

    setPermissionsData: (data: PermissionResponse) => {
      queryClient.setQueryData(permissionKeys.current(), data);
    },

    getPermissionsData: () => {
      return queryClient.getQueryData<PermissionResponse>(permissionKeys.current());
    },
  };
}

/**
 * Prefetch permissions for performance optimization
 */
export function usePrefetchPermissions() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.prefetchQuery({
      queryKey: permissionKeys.current(),
      queryFn: fetchPermissions,
      staleTime: 5 * 60 * 1000,
    });
  };
}

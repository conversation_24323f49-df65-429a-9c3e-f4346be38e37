# RBAC Implementation with Third-Party Role Integration

## Overview

This implementation adds Role-Based Access Control (RBAC) to your Next.js application with AWS Cognito authentication and Redis session management. It integrates with external APIs to fetch user roles and maps them to internal permissions.

## Features

- ✅ **External Role Integration**: Fetch roles from third-party APIs using email
- ✅ **Permission Mapping**: Map external roles to internal permissions
- ✅ **Redis Caching**: Cache roles to reduce API calls and improve performance
- ✅ **Middleware Protection**: Route-level permission checking
- ✅ **Server Actions**: Permission-based access control for server functions
- ✅ **Client Components**: React hooks and components for UI permission checks
- ✅ **Webhook Support**: Real-time role updates via webhooks
- ✅ **Error Handling**: Graceful fallbacks when external API fails

## Architecture

```
External API → Redis Cache → Session Storage → Middleware/RBAC → Protected Routes
```

## Environment Variables

Add these to your `.env.local` file:

```bash
# QBraid API Configuration (uses existing external client)
EXTERNAL_ROLES_ENDPOINT=/user/roles
QBRAID_API_URL=http://localhost:3001/api  # For development
# QBRAID_API_TOKEN=fallback-token  # Optional fallback token

# Webhook Security
WEBHOOK_SECRET_TOKEN=your-webhook-secret-token-for-role-updates

# Existing variables (Redis, Cognito, etc.)
REDIS_URL=redis://localhost:6379
SESSION_SECRET=your-secure-session-secret
```

## External API Integration

### Expected API Response Format

Your QBraid API endpoint should return roles in this format:

```json
{
  "roles": ["qbraid_admin", "qbraid_user", "device_manager"],
  "email": "<EMAIL>",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

The roles endpoint is called as: `GET /user/roles?email=<EMAIL>`

### Role Mapping Configuration

Edit `lib/permissions.ts` to customize role mappings for your QBraid roles:

```typescript
export const externalRoleToPermissions: ExternalRoleMapping = {
  qbraid_admin: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.AdminAccess,
    Permission.ViewTeam,
    Permission.ManageTeam,
  ],
  device_manager: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewJobs,
    Permission.ManageJobs,
  ],
  qbraid_user: [
    Permission.ViewDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewJobs,
  ],
};
```

## Usage Examples

### 1. Server Actions

```typescript
import { requirePermission, checkPermission } from '@/lib/rbac';
import { Permission } from '@/types/auth';

export async function deleteDevice(deviceId: string) {
  // Require specific permission
  await requirePermission(Permission.ManageDevices);

  // Your delete logic here
}

export async function getDeviceData() {
  // Check permission without throwing
  const canManage = await checkPermission(Permission.ManageDevices);

  if (canManage) {
    // Return sensitive data
  } else {
    // Return limited data
  }
}
```

### 2. Client Components

```tsx
import { usePermissions, PermissionGuard } from '@/hooks/use-permissions';
import { Permission } from '@/types/auth';

function DeviceManagement() {
  const { hasPermission, isAdmin } = usePermissions();

  return (
    <div>
      {/* Conditional rendering */}
      {hasPermission(Permission.ManageDevices) && <button>Delete Device</button>}

      {/* Permission guard component */}
      <PermissionGuard permission={Permission.AdminAccess} fallback={<p>Admin access required</p>}>
        <AdminPanel />
      </PermissionGuard>

      {/* Multiple permissions */}
      <PermissionGuard
        permissions={[Permission.ViewDevices, Permission.ManageDevices]}
        requireAll={false} // OR logic
      >
        <DeviceControls />
      </PermissionGuard>
    </div>
  );
}
```

### 3. Route Protection

Routes are automatically protected based on the mapping in `lib/permissions.ts`:

```typescript
export const routePermissions: Record<string, Permission> = {
  '/devices': Permission.ViewDevices,
  '/edit-device': Permission.ManageDevices,
  '/admin': Permission.AdminAccess,
};
```

### 4. Webhook Integration

Your QBraid system can update roles via webhook:

```bash
curl -X POST http://localhost:3000/api/update-roles \
  -H "Authorization: Bearer your-webhook-secret-token" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "roles": ["qbraid_admin", "device_manager"]
  }'
```

### 5. Manual Role Refresh

Users can refresh their roles:

```typescript
const refreshRoles = async () => {
  const response = await fetch('/api/refresh-roles', { method: 'POST' });
  if (response.ok) {
    // Optionally refresh the page or update state
    window.location.reload();
  }
};
```

## API Endpoints

- `POST /api/update-roles` - Webhook for external role updates
- `POST /api/refresh-roles` - Manual role refresh for current user
- `GET /api/auth/permissions` - Get current user's permissions and roles

## Error Handling

The system gracefully handles various failure scenarios:

1. **External API Down**: Users can still log in with empty roles
2. **Redis Down**: Falls back to JWT session storage
3. **Invalid Roles**: Unknown roles are ignored, user keeps valid permissions
4. **Network Timeouts**: 10-second timeout with fallback to empty roles

## Performance Optimizations

- **Redis Caching**: Roles cached for 1 hour (configurable)
- **Session Integration**: Roles stored in session for fast access
- **Parallel Fetching**: Role fetching happens during authentication
- **Batch Operations**: Multiple permission checks optimized

## Security Considerations

- **Webhook Verification**: All webhooks require secret token
- **Permission Validation**: All routes and actions validate permissions
- **Secure Defaults**: Users start with minimal permissions
- **Session Security**: Roles tied to secure session management

## Monitoring and Logging

The implementation includes comprehensive logging:

- `🔍 [EXTERNAL-ROLES]` - External API operations
- `💾 [EXTERNAL-ROLES]` - Caching operations
- `🎭 [AUTH]` - Role assignment during authentication
- `🔄 [WEBHOOK]` - Webhook operations
- `⚠️` - Warnings for fallback scenarios
- `❌` - Error conditions

## Testing

Test the implementation:

1. **Role Fetching**: Check logs during sign-in for role retrieval
2. **Permission Checking**: Try accessing protected routes
3. **Webhook Updates**: Test role updates via webhook
4. **Fallback Scenarios**: Disable external API to test fallbacks
5. **Cache Performance**: Monitor Redis for role caching

## Troubleshooting

### Common Issues

1. **No roles fetched**:
   - Check `EXTERNAL_ROLES_ENDPOINT` configuration (default: `/user/roles`)
   - Verify QBraid API is accessible and user has valid Cognito tokens
   - Check external client logs for authentication issues
   - Verify API response format matches expected structure

2. **Permissions not working**:
   - Verify role mapping in `lib/permissions.ts`
   - Check session contains roles and permissions
   - Ensure middleware is properly configured

3. **Webhook not working**:
   - Verify `WEBHOOK_SECRET_TOKEN` matches
   - Check webhook endpoint accessibility
   - Validate request format

4. **Cache issues**:
   - Check Redis connectivity
   - Monitor cache TTL settings
   - Verify Redis key prefixes

## Migration from Existing Auth

If you have existing authentication:

1. **Session Migration**: Existing sessions will work but won't have roles until next sign-in
2. **Route Protection**: New permission checks will be enforced immediately
3. **Backward Compatibility**: Existing auth checks continue to work

## Customization

### Adding New Permissions

1. Add to `Permission` enum in `types/auth.ts`
2. Update role mappings in `lib/permissions.ts`
3. Add route mappings if needed
4. Use in components and server actions

### Custom External API Format

Modify `fetchRolesFromExternalApi` in `lib/external-roles.ts` to match your API's response format.

### Custom Caching Strategy

Adjust cache TTL and key patterns in `lib/external-roles.ts` as needed.

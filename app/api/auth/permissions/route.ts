import { NextResponse } from 'next/server';
import { getCurrentPermissions, getCurrentRoles } from '@/lib/rbac';

/**
 * Enhanced API endpoint for current user's permissions and roles
 * Optimized for TanStack Query with proper cache headers
 */
export async function GET() {
  try {
    const [permissions, roles] = await Promise.all([getCurrentPermissions(), getCurrentRoles()]);

    const response = {
      success: true,
      permissions,
      roles,
      timestamp: new Date().toISOString(),
    };

    // Create response with cache headers for TanStack Query optimization
    const nextResponse = NextResponse.json(response);

    // Add cache control headers
    nextResponse.headers.set('Cache-Control', 'private, max-age=300, must-revalidate'); // 5 minutes
    nextResponse.headers.set('ETag', `W/"${Date.now()}"`);
    nextResponse.headers.set('Vary', '<PERSON>ie, Authorization');

    // Add custom headers for client-side caching hints
    nextResponse.headers.set('X-Permissions-Count', permissions.length.toString());
    nextResponse.headers.set('X-Roles-Count', roles.length.toString());

    return nextResponse;
  } catch (error) {
    console.error('❌ [PERMISSIONS-API] Failed to get permissions:', error);

    // Return empty permissions if user is not authenticated
    const errorResponse = NextResponse.json(
      {
        success: false,
        permissions: [],
        roles: [],
        error: 'Authentication required',
        timestamp: new Date().toISOString(),
      },
      { status: 401 },
    );

    // Even error responses should have cache headers to prevent excessive retries
    errorResponse.headers.set('Cache-Control', 'private, max-age=60, must-revalidate'); // 1 minute for errors

    return errorResponse;
  }
}

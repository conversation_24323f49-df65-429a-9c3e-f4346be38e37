import { NextResponse } from 'next/server';
import { getCurrentPermissions, getCurrentRoles } from '@/lib/rbac';

/**
 * API endpoint to get current user's permissions and roles
 * Used by client-side hooks for permission checking
 */
export async function GET() {
  try {
    const [permissions, roles] = await Promise.all([getCurrentPermissions(), getCurrentRoles()]);

    return NextResponse.json({
      success: true,
      permissions,
      roles,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ [PERMISSIONS-API] Failed to get permissions:', error);

    // Return empty permissions if user is not authenticated
    return NextResponse.json(
      {
        success: false,
        permissions: [],
        roles: [],
        error: 'Authentication required',
      },
      { status: 401 },
    );
  }
}

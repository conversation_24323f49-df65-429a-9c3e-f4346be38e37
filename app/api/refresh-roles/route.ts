import { NextRequest, NextResponse } from 'next/server';
import { getRolesForUser, invalidateUserRoles } from '@/lib/external-roles';
import { getSession } from '@/lib/session';

/**
 * API endpoint to refresh current user's roles from external API
 * Useful for when roles change and user wants immediate update
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();

    if (!session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const email = session.email;
    console.log(`🔄 [REFRESH-ROLES] Refreshing roles for ${email}`);

    // Invalidate cache first
    await invalidateUserRoles(email);

    // Fetch fresh roles from external API
    const newRoles = await getRolesForUser(email, true); // force refresh

    console.log(`✅ [REFRESH-ROLES] Retrieved ${newRoles.length} roles for ${email}:`, newRoles);

    return NextResponse.json({
      success: true,
      message: 'Roles refreshed successfully',
      data: {
        email,
        roles: newRoles,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('❌ [REFRESH-ROLES] Failed to refresh roles:', error);
    return NextResponse.json({ error: 'Failed to refresh roles' }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    service: 'Role Refresh API',
    description: 'POST to refresh current user roles from external API',
  });
}

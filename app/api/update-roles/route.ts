import { NextRequest, NextResponse } from 'next/server';
import { updateUserRoles, invalidateUserRoles } from '@/lib/external-roles';

/**
 * Webhook endpoint for external role updates
 * This endpoint should be called by your third-party system when user roles change
 */
export async function POST(request: NextRequest) {
  try {
    // Verify webhook signature or API key for security
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.WEBHOOK_SECRET_TOKEN;

    if (!expectedToken || authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { email, roles, action } = body;

    if (!email || typeof email !== 'string') {
      return NextResponse.json(
        { error: 'Email is required and must be a string' },
        { status: 400 },
      );
    }

    if (action === 'delete' || action === 'invalidate') {
      // Invalidate cached roles
      await invalidateUserRoles(email);
      console.log(`🗑️ [WEBHOOK] Invalidated roles for ${email}`);

      return NextResponse.json({
        success: true,
        message: `Roles invalidated for ${email}`,
      });
    }

    if (!Array.isArray(roles)) {
      return NextResponse.json({ error: 'Roles must be an array' }, { status: 400 });
    }

    // Update cached roles
    await updateUserRoles(email, roles);
    console.log(`🔄 [WEBHOOK] Updated roles for ${email}:`, roles);

    return NextResponse.json({
      success: true,
      message: `Roles updated for ${email}`,
      data: { email, roles },
    });
  } catch (error) {
    console.error('❌ [WEBHOOK] Role update failed:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Support GET for webhook verification
export async function GET() {
  return NextResponse.json({
    service: 'Role Update Webhook',
    status: 'active',
    timestamp: new Date().toISOString(),
  });
}

import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import type { TeamMember } from '@/types/team';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ params: string[] }> },
) {
  try {
    const resolvedParams = await params;
    const [orgId, page, pageSize] = resolvedParams.params;

    if (!orgId || !page || !pageSize) {
      return NextResponse.json(
        {
          error: 'Organization ID, page, and pageSize are required',
        },
        { status: 400 },
      );
    }

    // Proxy to external API
    const response = await externalClient.get(`/orgs/users/${orgId}/${page}/${pageSize}`);
    const data = response.data;
    const orgUsers = data.orgUsers || [];
    const pagination = data.pagination || {};

    // Enrich users with profile data
    const enrichedUsers: TeamMember[] = await Promise.all(
      orgUsers.map(async (user: TeamMember) => {
        let profile = {} as any;

        try {
          const profileResponse = await externalClient.get(`/get-user-details`, {
            email: user.email,
          });
          profile = profileResponse.data;
        } catch (err) {
          console.error('❌ [API Gateway] Error fetching user profile for:', user.email, err);
        }

        return {
          name:
            profile.firstName && profile.lastName
              ? `${profile.firstName} ${profile.lastName}`
              : user.email,
          email: user.email,
          firstName: profile.firstName,
          lastName: profile.lastName,
          avatar: profile.profilePhoto,
          role: user.role.charAt(0).toUpperCase() + user.role.slice(1),
          status:
            user.status === 'Active'
              ? 'Active'
              : user.status === 'Invited'
                ? 'Invited'
                : 'Deactivated',
          userCredits: Math.round(user.userCredits ?? 0),
          lastActive: user.lastActive ? formatLastActive(user.lastActive) : 'Never',
        };
      }),
    );

    return NextResponse.json({
      users: enrichedUsers,
      totalUsers: pagination.totalUsers ?? 0,
    });
  } catch (error: any) {
    console.error('❌ [API Gateway] Error fetching organization users:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch organization users',
        users: [],
        totalUsers: 0,
      },
      { status: error.status || 500 },
    );
  }
}

// Helper function to format last active date
function formatLastActive(date: string): string {
  try {
    const lastActive = new Date(date);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - lastActive.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  } catch {
    return 'Never';
  }
}

import axios, { AxiosInstance, AxiosError } from 'axios';
import { getCognitoTokenCookies, getUserEmailFromToken, getCurrentSessionId } from '@/lib/session';

// Determine base URL based on environment
const env = process.env.NODE_ENV;
const customEnv = process.env.NEXT_PUBLIC_NODE_ENV;
const baseURL =
  env === 'production'
    ? 'https://api.qbraid.com/api'
    : customEnv === 'staging'
      ? 'https://api-staging-1.qbraid.com/api'
      : process.env.QBRAID_API_URL || 'http://localhost:3001/api';

// Types for API responses and errors
interface APIErrorResponse {
  message?: string;
  error?: string;
  [key: string]: unknown;
}

/**
 * Server-side HTTP client for external QBraid API
 * Handles authentication and request/response transformations
 */
class ExternalAPIClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: { 'Content-Type': 'application/json' },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor for auth with session-aware token retrieval
    this.client.interceptors.request.use(
      async (config) => {
        try {
          // Get current session ID for Redis-based token retrieval
          const sessionId = await getCurrentSessionId();
          const tokens = await getCognitoTokenCookies(sessionId || undefined);
          const userEmail = await getUserEmailFromToken();

          if (tokens.accessToken) {
            config.headers.authorization = `Bearer ${tokens.accessToken}`;
            console.log(
              `🔐 [API-CLIENT] Using access token from ${sessionId ? 'Redis' : 'cookies'}`,
            );
          }

          if (userEmail) {
            config.headers.email = userEmail;
          }

          // Fallback to env variables if needed
          if (!tokens.accessToken && process.env.QBRAID_API_TOKEN) {
            config.headers.authorization = `Bearer ${process.env.QBRAID_API_TOKEN}`;
            console.log('🔐 [API-CLIENT] Using fallback API token from environment');
          }

          if (!userEmail && process.env.QBRAID_API_EMAIL) {
            config.headers.email = process.env.QBRAID_API_EMAIL;
          }

          console.log(`🔄 [External API] ${config.method?.toUpperCase()} ${config.url}`);
        } catch (error) {
          console.error('❌ [External API] Auth setup failed:', error);
        }

        return config;
      },
      (error) => Promise.reject(error),
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        console.log(
          `✅ [External API] ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`,
        );
        return response;
      },
      (error: AxiosError) => {
        console.error(
          `❌ [External API] ${error.config?.method?.toUpperCase()} ${error.config?.url} - ${error.response?.status || 'Network Error'}`,
        );

        // Transform external API errors to consistent format
        const errorResponse = {
          error: true,
          message:
            (error.response?.data as APIErrorResponse)?.message ||
            error.message ||
            'External API error',
          status: error.response?.status || 500,
          data: error.response?.data,
        };

        return Promise.reject(errorResponse);
      },
    );
  }

  // Generic methods for different HTTP verbs
  async get(url: string, params?: Record<string, unknown>) {
    return this.client.get(url, { params });
  }

  async post(url: string, data?: Record<string, unknown>) {
    return this.client.post(url, data);
  }

  async put(url: string, data?: Record<string, unknown>) {
    return this.client.put(url, data);
  }

  async patch(url: string, data?: Record<string, unknown>) {
    return this.client.patch(url, data);
  }

  async delete(url: string) {
    return this.client.delete(url);
  }
}

// Export singleton instance
export const externalClient = new ExternalAPIClient();
export default externalClient;

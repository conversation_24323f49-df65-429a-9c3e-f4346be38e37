'use client';

import { Search, Filter, Download, ChevronDown, UserPlus } from 'lucide-react';
import { useState, useMemo } from 'react';

import { toast } from 'sonner';

import { InviteUsers } from '@/components/invite-member-button';
import { TeamMemberRow } from '@/components/team-member-row';
import { RemoveUserButton } from '@/components/remove-user-button';
import { ChangeRoleButton } from '@/components/change-role-button';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { RoleCard } from '@/components/role-card';
import { ActionLogRow } from '@/components/action-log-row';
import type { ActionLogRowProps } from '@/types/logs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useOrgUsers, useOrgInfo, useActionLogs, useManipulateUsers } from '@/hooks/use-api';
import type { TeamMember } from '@/types/team';
import { z } from 'zod';

interface LogDisplayProps {
  logs: ActionLogRowProps[];
}

const roles = [
  {
    name: 'Owner',
    description: 'Has full control over the organization, including destructive actions.',
    permissions: [
      'Edit organization settings',
      'Purchase credits',
      'Transfer credits to others',
      'Delete the organization',
    ],
    members: [],
  },
  {
    name: 'SuperAdmin',
    description: 'Manages the organization without destructive permissions.',
    permissions: [
      'All permissions of Owner except deleting the organization',
      'Manage credits',
      'Manage users',
    ],
    members: [],
  },
  {
    name: 'Admin',
    description: 'Manages credit flow and user access at a high level.',
    permissions: [
      'Transfer and allocate credits to managers and users',
      'View credit usage',
      'Cannot purchase credits',
    ],
    members: [],
  },
  {
    name: 'Manager',
    description: 'Oversees and supports regular users in the organization.',
    permissions: ['Manage users with the User role', 'Distribute credits to users'],
    members: [],
  },
  {
    name: 'User',
    description: 'Has access to use allocated credits but no admin permissions.',
    permissions: ['Use allocated credits only', 'No visibility or control over the organization'],
    members: [],
  },
];

const actions = [
  'Add New Device',
  'Change Device Info',
  'Delete Device',
  'Invite User',
  'Remove User',
  'Change User Role',
];

const inviteUserSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  role: z.string().min(1, { message: 'Role is required' }),
});

export default function TeamPage() {
  // Dialog state for success/error messages
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [actionDialogMessage, setActionDialogMessage] = useState('');
  const [actionDialogType, setActionDialogType] = useState<'success' | 'error'>('success');

  // Team Member state
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [roleFilter, setRoleFilter] = useState<string>('All Roles');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);

  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(5);
  const [totalUsers, setTotalUsers] = useState(1);
  const [users, setUsers] = useState<TeamMember[]>([]);

  const [total, setTotal] = useState(0);
  const totalPages = Math.ceil(total / pageSize);

  // Fetch org users and org info using React Query API hooks instead of useEffect
  // TODO: Make orgID dynamic as needed
  const orgID = '683f2d0f36e1cc5bd8f3ecec';
  const { data: usersData, isLoading: usersLoading } = useOrgUsers(orgID, page, pageSize);
  const { data: orgInfo, isLoading: orgInfoLoading } = useOrgInfo('testing-1');

  // For the remove users button
  const [removeUser, setRemoveUser] = useState<TeamMember | null>(null);

  // For the change role button
  const [changeRoleUser, setChangeRoleUser] = useState<TeamMember | null>(null);

  // For inviting a new member
  const [inviteOpen, setInviteOpen] = useState(false);
  const [, setInviteLoading] = useState(false);
  const [, setInviteError] = useState<string | null>(null);

  // Action log state
  const [actionLogs, setActionLogs] = useState<LogDisplayProps>({
    logs: [],
  });
  const [actionLogFilter, setActionLogFilter] = useState<string>('All Actions');
  const [actionLogSearch, setActionLogSearch] = useState<string>('');
  const [actionLogPage, setActionLogPage] = useState(0);
  const [actionLogResultsPerPage, setActionLogResultsPerPage] = useState(10);
  const [totalActionLogs, setTotalActionLogs] = useState(0);
  const [loadingActionLogs, setLoadingActionLogs] = useState(false);
  const totalActionLogPages = Math.ceil(totalActionLogs / actionLogResultsPerPage);

  // Fetch action logs using React Query API hook
  const provider = 'AWS';
  const { data: actionLogsData, isLoading: actionLogsLoading } = useActionLogs(
    provider,
    actionLogPage,
    actionLogResultsPerPage,
  );

  const { mutate: manipulateUsers } = useManipulateUsers();

  function formatLastActive(dateString?: string): string {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays > 1 && diffDays < 7) {
      return `${diffDays} days ago`;
    }

    return date.toLocaleDateString();
  }

  const filteredUsers = useMemo(
    () =>
      (users || []).filter((member) => {
        const matchesRole =
          roleFilter === 'All Roles' || member.role.toLowerCase() === roleFilter.toLowerCase();
        const matchesSearch =
          member.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          member.email?.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesRole && matchesSearch;
      }),
    [users, roleFilter, searchTerm],
  );

  // Filter action logs based on selected action
  const filteredActionLogs = useMemo(
    () =>
      (actionLogs || []).logs.filter((log) => {
        const matchesAction =
          actionLogFilter === 'All Actions' ||
          log.action.toLowerCase() === actionLogFilter.toLowerCase();
        const matchesSearch =
          log.userEmail.toLowerCase().includes(actionLogSearch.toLowerCase()) ||
          log.actionDescription?.toLowerCase().includes(actionLogSearch.toLowerCase()) ||
          log.userRole?.toLowerCase().includes(actionLogSearch.toLowerCase());
        return matchesAction && matchesSearch;
      }),
    [actionLogs.logs, actionLogFilter, actionLogSearch],
  );

  // Function to invite a new member
  const handleInviteUser = async (values: z.infer<typeof inviteUserSchema>) => {
    const body = JSON.stringify({
      email: values.email,
      role: values.role,
    });

    await new Promise((resolve, reject) => {
      manipulateUsers(
        { action: 'invite', body },
        {
          onSuccess: () => resolve(true),
          onError: (error) => reject(error),
        },
      );
    });

    setActionDialogOpen(true);
  };

  const handleResendInvite = async (email: string) => {
    const body = JSON.stringify({ email });
    await new Promise((resolve, reject) => {
      manipulateUsers(
        { action: 'resend-invite', body },
        {
          onSuccess: () => resolve(true),
          onError: (error) => reject(error),
        },
      );
    });
    toast.success(`An invite has been resent to ${email}`);
  };

  // Function to remove a user
  const handleRemoveUser = async () => {
    if (!removeUser) return;

    let body = JSON.stringify({
      email: removeUser.email,
      name: orgInfo?.orgName,
    });

    let result = await new Promise((resolve, reject) => {
      manipulateUsers(
        { action: 'remove', body },
        {
          onSuccess: () => resolve(true),
          onError: (error) => reject(error),
        },
      );
    });
    if (result) {
      setTeamMembers((prev) => prev.filter((member) => member.email !== removeUser.email));
    }

    setActionDialogOpen(true);
    setRemoveUser(null);
  };

  // Function to change the role of a user
  const handleChangeRole = async (user: TeamMember, newRole: string) => {
    if (!user || !newRole) return;

    let body = JSON.stringify({
      email: user.email,
      role: newRole,
      name: orgInfo?.orgName,
    });

    let result = await new Promise((resolve, reject) => {
      manipulateUsers(
        { action: 'change-role', body },
        {
          onSuccess: () => resolve(true),
          onError: (error) => reject(error),
        },
      );
    });

    if (result) {
      setTeamMembers((prev) =>
        prev.map((member) => (member.email === user.email ? { ...member, role: newRole } : member)),
      );
    }

    setActionDialogOpen(true);
    setChangeRoleUser(null);
  };

  const rolesWithMembers = roles.map((role) => ({
    ...role,
    members: teamMembers
      .filter((member) => member.role.toLowerCase() === role.name.toLowerCase())
      .map((member) => ({
        name: member.name,
        avatar: member.avatar,
        email: member.email,
      })),
  }));

  return (
    <>
      {inviteOpen && (
        <InviteUsers
          roles={roles}
          onInviteUser={(email, role) => handleInviteUser({ email, role })}
          onCancel={() => setInviteOpen(false)}
        />
      )}

      {changeRoleUser && (
        <ChangeRoleButton
          user={changeRoleUser}
          roles={roles}
          onRoleChange={handleChangeRole}
          onClose={() => setChangeRoleUser(null)}
        />
      )}

      {removeUser && (
        <RemoveUserButton
          user={removeUser}
          onRemove={handleRemoveUser}
          onClose={() => setRemoveUser(null)}
        />
      )}

      <div className="min-h-screen bg-[#18141f] overflow-x-auto">
        {/* Navbar provided by root layout */}

        <main className="p-6 space-y-8">
          {/* Team Management page */}
          <section>
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-white text-2xl font-bold mb-1">
                  Team Management
                  <span className="ml-2 text-[#8a2be2] text-base font-normal">
                    ({loading ? 'Loading...' : `${totalUsers} User${totalUsers === 1 ? '' : 's'}`})
                  </span>
                </h1>
                <p className="text-[#94a3b8] text-sm">View, Edit, and Invite Team Members</p>
              </div>

              {/* TODO - need to implement a way to allow only owner, superadmin, and admin to be able to invite members */}
              {/* TODO - have managers be able to invite members but only assign them user role*/}
              {/* TODO - Users should not have any acess to invited members*/}
              <Button
                aria-label="Invite Member"
                className="bg-[#8a2be2] hover:bg-[#8a2be2]/90 text-white"
                onClick={() => setInviteOpen(true)}
              >
                <UserPlus className="w-4 h-4 mr-2" />
                Invite Member
              </Button>
            </div>

            {/* team members search bar */}
            <div className="flex items-center space-x-4 mb-6">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#94a3b8] w-4 h-4" />
                <Input
                  placeholder="Search team members..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-[#262131] border-[#3b3b3b] text-white placeholder:text-[#94a3b8]"
                />
              </div>

              {/* Team managment search DropDown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    aria-label="Filter By Role"
                    variant="outline"
                    className="bg-[#262131] border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white"
                  >
                    <Filter className="w-4 h-4 mr-2" />
                    {roleFilter}
                    <ChevronDown className="w-4 h-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-[#262131] border-[#3b3b3b]">
                  <DropdownMenuItem
                    className="text-[#94a3b8] hover:text-white hover:bg-[#3b3b3b]"
                    onClick={() => setRoleFilter('All Roles')}
                  >
                    All Roles
                  </DropdownMenuItem>
                  {roles.map((role) => (
                    <DropdownMenuItem
                      key={role.name}
                      className="text-[#94a3b8] hover:text-white hover:bg-[#3b3b3b]"
                      onClick={() => setRoleFilter(role.name)}
                    >
                      {role.name}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* pagination for team table */}
            <div className="flex items-center justify-between mt-4 flex-wrap gap-2 mb-4">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  className="px-3 py-1 rounded bg-transparent border-[#3b3b3b] text-white hover:bg-[#3b3b3b] disabled:opacity-50"
                  onClick={() => setPage((p) => Math.max(0, p - 1))}
                  disabled={page === 0}
                >
                  Previous
                </Button>

                <span className="text-[#94a3b8]">
                  Page {page + 1} of {Math.max(1, Math.ceil(totalUsers / pageSize))}
                </span>

                <Button
                  variant="outline"
                  className="px-3 py-1 rounded bg-transparent border-[#3b3b3b] text-white hover:bg-[#3b3b3b] disabled:opacity-50"
                  onClick={() =>
                    setPage((p) => Math.min(p + 1, Math.ceil(totalUsers / pageSize - 1)))
                  }
                  disabled={page >= Math.ceil(totalUsers / pageSize) - 1}
                >
                  Next
                </Button>
              </div>

              {/* Rows per page selector */}
              <div className="flex items-center space-x-2">
                <span className="text-[#94a3b8]">Rows per page:</span>
                <select
                  value={pageSize}
                  onChange={(e) => {
                    setPageSize(Number(e.target.value));
                    setPage(0);
                  }}
                  className="px-2 py-1 rounded bg-[#262131] border border-[#3b3b3b] text-white hover:bg-[#3b3b3b]"
                >
                  {[5, 10, 25, 100].map((size) => (
                    <option key={size} value={size}>
                      {size}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/*  Table of Org users - emails roles status etc table */}
            <Card className="bg-[#262131] border-[#3b3b3b]">
              <CardContent className="p-0">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-[#3b3b3b]">
                      <th
                        scope="col"
                        className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm"
                      >
                        User
                      </th>
                      <th
                        scope="col"
                        className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm"
                      >
                        Role
                      </th>
                      <th
                        scope="col"
                        className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm"
                      >
                        Credits
                      </th>
                      <th
                        scope="col"
                        className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm"
                      >
                        Status
                      </th>
                      <th
                        scope="col"
                        className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm"
                      >
                        Last Active
                      </th>
                      <th
                        scope="col"
                        className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm"
                      >
                        More
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={6} className="text-center py-6 text-[#94a3b8]">
                          Loading team members...
                        </td>
                      </tr>
                    ) : users.length === 0 ? (
                      <tr>
                        <td colSpan={6} className="text-center py-6 text-[#94a3b8]">
                          No team members found.
                        </td>
                      </tr>
                    ) : (
                      filteredUsers.map((member) => (
                        <TeamMemberRow
                          key={member.email}
                          user={{
                            ...member,
                            userCredits: member.userCredits ?? 0,
                            lastActive: member.lastActive ?? '',
                          }}
                          onChangeRole={() => setChangeRoleUser(member)}
                          onRemove={() => setRemoveUser(member)}
                        />
                      ))
                    )}
                  </tbody>
                </table>
              </CardContent>
            </Card>
          </section>

          {/* Roles and Permissions Section */}
          <section>
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-white text-2xl font-bold mb-1">Roles and Permissions</h2>
                <p className="text-[#94a3b8] text-sm">
                  Manage permissions and assign them to users
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {rolesWithMembers.map((role) => (
                <RoleCard key={role.name} role={role} />
              ))}
            </div>
          </section>

          {/* Action Log Section */}
          <section>
            <div className="mb-6">
              <h2 id="view-action-logs" className="text-white text-2xl font-bold mb-1">
                Action Log
              </h2>
              <p className="text-[#94a3b8] text-sm">
                View a log of actions such as adding a new device, changing device info, changing
                role permissions, assigning a role to a member, etc.
              </p>
            </div>

            {/* Search and Filter Controls */}
            <div className="flex items-center space-x-4 mb-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#94a3b8] w-4 h-4" />
                <Input
                  placeholder="Search logs..."
                  value={actionLogSearch}
                  onChange={(e) => setActionLogSearch(e.target.value)}
                  className="pl-10 bg-[#262131] border-[#3b3b3b] text-white placeholder:text-[#94a3b8]"
                />
              </div>

              {/* Filter by Action dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    aria-label="Filter by Action"
                    variant="outline"
                    className="bg-[#262131] border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white"
                  >
                    <Filter className="w-4 h-4 mr-2" />
                    {actionLogFilter}
                    <ChevronDown className="w-4 h-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-[#262131] border-[#3b3b3b]">
                  <DropdownMenuItem
                    className="text-[#94a3b8] hover:text-white hover:bg-[#3b3b3b]"
                    onClick={() => setActionLogFilter('All Actions')}
                  >
                    All Actions
                  </DropdownMenuItem>
                  {actions.map((action) => (
                    <DropdownMenuItem
                      key={action}
                      className="text-[#94a3b8] hover:text-white hover:bg-[#3b3b3b]"
                      onClick={() => setActionLogFilter(action)}
                    >
                      {action}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Download Action Log Button */}
              <Button
                aria-label="Download Action Log"
                variant="outline"
                size="sm"
                className="bg-[#262131] border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white"
              >
                <Download className="w-4 h-4" />
              </Button>
            </div>

            {/* Pagination Controls */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2 mr-4">
                <Button
                  variant="outline"
                  className="px-3 py-1 rounded bg-transparent border-[#3b3b3b] text-white hover:bg-[#3b3b3b]"
                  onClick={() => {
                    setActionLogPage((p) => Math.max(0, p - 1));
                    document
                      .getElementById('view-action-logs')
                      ?.scrollIntoView({ behavior: 'smooth' });
                  }}
                  disabled={actionLogPage === 0}
                >
                  Previous
                </Button>
                <span className="text-[#94a3b8]">
                  Page {actionLogPage + 1} of {totalActionLogPages || 1}
                </span>
                <Button
                  variant="outline"
                  className="px-3 py-1 rounded bg-transparent border-[#3b3b3b] text-white hover:bg-[#3b3b3b]"
                  onClick={() => {
                    setActionLogPage((p) => Math.min(totalActionLogPages - 1, p + 1));
                    document
                      .getElementById('view-action-logs')
                      ?.scrollIntoView({ behavior: 'smooth' });
                  }}
                  disabled={actionLogPage >= totalActionLogPages - 1}
                >
                  Next
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-[#94a3b8]">Rows per page:</span>
                <select
                  className="px-2 py-1 rounded bg-[#262131] border-[#3b3b3b] text-white hover:bg-[#3b3b3b]"
                  value={actionLogResultsPerPage}
                  onChange={(e) => {
                    setActionLogResultsPerPage(Number(e.target.value));
                    setActionLogPage(0);
                    document
                      .getElementById('view-action-logs')
                      ?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  {[5, 10, 20, 50].map((opt) => (
                    <option key={opt} value={opt}>
                      {opt}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Action log table */}
            <Card className="bg-[#262131] border-[#3b3b3b] mb-6">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-[#3b3b3b]">
                        <th
                          scope="col"
                          className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm"
                        >
                          User
                        </th>
                        <th
                          scope="col"
                          className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm"
                        >
                          Role
                        </th>
                        <th
                          scope="col"
                          className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm"
                        >
                          Action
                        </th>
                        <th
                          scope="col"
                          className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm"
                        >
                          Description
                        </th>
                        <th
                          scope="col"
                          className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm"
                        >
                          Timestamp
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {loadingActionLogs ? (
                        <tr>
                          <td colSpan={5} className="text-center py-6 text-[#94a3b8]">
                            Loading action logs...
                          </td>
                        </tr>
                      ) : filteredActionLogs.length === 0 ? (
                        <tr>
                          <td colSpan={5} className="text-center py-6 text-[#94a3b8]">
                            No action logs found.
                          </td>
                        </tr>
                      ) : (
                        filteredActionLogs.map((log) => (
                          <ActionLogRow
                            key={`${log.timestamp}-${log.action}-${log.user}`}
                            {...log}
                          />
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </section>
        </main>
      </div>
      <Dialog open={actionDialogOpen} onOpenChange={setActionDialogOpen}>
        <DialogContent className="text-center bg-[#262131] ">
          <DialogTitle className="text-white">
            {actionDialogType === 'success' ? 'Success' : 'Error'}
          </DialogTitle>
          <div className={actionDialogType === 'success' ? 'text-green-500' : 'text-red-500'}>
            {actionDialogMessage}
          </div>
          <button
            className="mt-4 px-4 py-2 bg-purple-700 text-white rounded"
            onClick={() => setActionDialogOpen(false)}
          >
            Close
          </button>
        </DialogContent>
      </Dialog>
    </>
  );
}

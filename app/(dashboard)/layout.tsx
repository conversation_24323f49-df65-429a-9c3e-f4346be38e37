'use client';

import { Inter } from 'next/font/google';
import { usePathname } from 'next/navigation';
import { Navigation } from '@/components/navigation';
import Footer from '@/components/footer';

const inter = Inter({ subsets: ['latin'] });

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const segment = pathname.split('/')[1] || 'devices';

  return (
    <div className={`${inter.className} min-h-screen flex flex-col`}>
      {/* Navigation */}
      <Navigation activeTab={segment} />

      {/* Main Content */}
      <main className="flex-1 pt-16 pb-40 md:pb-20 lg:pb-20 bg-[#18141f]">{children}</main>

      {/* Footer */}
      <Footer />
    </div>
  );
}

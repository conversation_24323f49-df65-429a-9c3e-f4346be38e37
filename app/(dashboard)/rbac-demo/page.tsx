import { Metadata } from 'next';
import { PermissionExamples } from '@/components/examples/permission-examples';

export const metadata: Metadata = {
  title: 'RBAC Demo - QBraid Partner Dashboard',
  description: 'Demonstration of Role-Based Access Control with QBraid organization roles',
};

/**
 * RBAC Demo Page
 * 
 * This page demonstrates the complete RBAC system implementation
 * with real-time permission checking and role-based UI components.
 */
export default function RBACDemoPage() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight mb-2">
          RBAC System Demo
        </h1>
        <p className="text-lg text-muted-foreground mb-4">
          Interactive demonstration of Role-Based Access Control with QBraid organization roles
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="font-semibold text-blue-800 mb-2">About This Demo</h2>
          <p className="text-blue-700 text-sm">
            This page showcases how the RBAC system works with different QBraid organization roles 
            (admin, member, viewer, owner). The UI components dynamically show/hide based on your 
            current permissions fetched from the QBraid organizations API.
          </p>
        </div>
      </div>

      <PermissionExamples />
    </div>
  );
}

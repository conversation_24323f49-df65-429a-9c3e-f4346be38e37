'use client';

import { useState } from 'react';
import { EarningsPlanCard } from '@/components/earnings-plan-card';

export default function EarningsPage() {
  const [selectedPlan, setSelectedPlan] = useState<'payg' | 'contract'>('payg');

  return (
    <div className="min-h-screen bg-[#18141f] overflow-x-auto">
      {/* Navbar provided by root layout */}

      <main className="p-6">
        <div className="mb-8">
          <h1 className="text-white text-3xl font-bold mb-2">Available Plans</h1>
          <p className="text-[#94a3b8]">
            We currently offer two plans for you to view and track your earnings.
          </p>
        </div>

        <EarningsPlanCard planType={selectedPlan} isActive={true} onPlanChange={setSelectedPlan} />
      </main>
    </div>
  );
}

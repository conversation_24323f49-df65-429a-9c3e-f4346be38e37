'use client';

import { useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface QubitTopologyProps {
  qubits: number;
  error2QBest: string;
  error2QLayered: string;
  clops: string;
}

export function QubitTopology({ qubits, error2QBest, error2QLayered, clops }: QubitTopologyProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // Define hexagon grid parameters
    const hexRadius = 18;
    const hexHeight = hexRadius * Math.sqrt(3);
    const hexWidth = hexRadius * 2;
    const horizontalSpacing = hexWidth * 0.9;
    const verticalSpacing = hexHeight * 0.75;

    // Define colors for different qubit states
    const colors = {
      normal: '#3b82f6', // blue
      error: '#ef4444', // red
      warning: '#f59e0b', // amber
      selected: '#8b5cf6', // purple
    };

    // Define which qubits should have special colors (based on the image)
    const specialQubits: Record<number, keyof typeof colors> = {
      1: 'warning',
      15: 'error',
      27: 'warning',
      32: 'error',
      52: 'warning',
      76: 'error',
      89: 'error',
      94: 'warning',
      118: 'warning',
    };

    // Calculate grid dimensions based on number of qubits
    const gridWidth = 16; // Approximate width of the grid in hexagons
    const gridHeight = Math.ceil(qubits / gridWidth);

    // Draw hexagons in a grid pattern
    let qubitIndex = 0;

    for (let row = 0; row < gridHeight; row++) {
      const rowOffset = (row % 2) * (horizontalSpacing / 2);

      for (let col = 0; col < gridWidth; col++) {
        if (qubitIndex >= qubits) break;

        const x = col * horizontalSpacing + rowOffset + 50;
        const y = row * verticalSpacing + 50;

        // Draw hexagon
        const color = specialQubits[qubitIndex] || 'normal';
        drawHexagon(ctx, x, y, hexRadius, colors[color], qubitIndex.toString());

        qubitIndex++;
      }
    }

    // Function to draw a hexagon with a number inside
    function drawHexagon(
      ctx: CanvasRenderingContext2D,
      x: number,
      y: number,
      radius: number,
      color: string,
      label: string,
    ) {
      ctx.beginPath();
      for (let i = 0; i < 6; i++) {
        const angle = (Math.PI / 3) * i;
        const hx = x + radius * Math.cos(angle);
        const hy = y + radius * Math.sin(angle);
        if (i === 0) {
          ctx.moveTo(hx, hy);
        } else {
          ctx.lineTo(hx, hy);
        }
      }
      ctx.closePath();

      // Fill hexagon
      ctx.fillStyle = color;
      ctx.fill();

      // Add text
      ctx.fillStyle = 'white';
      ctx.font = '10px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(label, x, y);
    }
  }, [qubits]);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-4 gap-8">
        <div>
          <p className="text-[#94a3b8] text-sm">Qubits</p>
          <p className="text-white text-2xl font-bold">{qubits}</p>
        </div>
        <div>
          <p className="text-[#94a3b8] text-sm">2Q Error (Best)</p>
          <p className="text-white text-2xl font-bold">{error2QBest}</p>
        </div>
        <div>
          <p className="text-[#94a3b8] text-sm">2Q Error (Layered)</p>
          <p className="text-white text-2xl font-bold">{error2QLayered}</p>
        </div>
        <div>
          <p className="text-[#94a3b8] text-sm">CLOPS</p>
          <p className="text-white text-2xl font-bold">{clops}</p>
        </div>
      </div>

      <Card className="bg-[#1d1825] border-[#3b3b3b]">
        <CardContent className="p-4">
          <canvas ref={canvasRef} className="w-full h-[500px]" />
        </CardContent>
      </Card>
    </div>
  );
}

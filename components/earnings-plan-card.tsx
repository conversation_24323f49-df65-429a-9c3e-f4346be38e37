'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Download, Edit, ExternalLink, Check } from 'lucide-react';

interface EarningsPlanCardProps {
  planType: 'payg' | 'contract';
  isActive: boolean;
  onPlanChange: (plan: 'payg' | 'contract') => void;
}

export function EarningsPlanCard({ planType, isActive, onPlanChange }: EarningsPlanCardProps) {
  const isContract = planType === 'contract';

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {/* Left Side - Plan Selection and Summary */}
      <div className="space-y-6">
        {/* Plan Tabs */}
        <div className="flex space-x-2">
          <Button
            variant={planType === 'payg' ? 'default' : 'outline'}
            className={`px-6 py-2 rounded-lg ${
              planType === 'payg'
                ? 'bg-[#8a2be2] text-white hover:bg-[#8a2be2]/90'
                : 'bg-[#262131] border-[#3b3b3b] text-[#94a3b8] hover:bg-[#262131]/80 hover:text-white'
            }`}
            onClick={() => onPlanChange('payg')}
          >
            Pay-As-You-Go
          </Button>
          <Button
            variant={planType === 'contract' ? 'default' : 'outline'}
            className={`px-6 py-2 rounded-lg ${
              planType === 'contract'
                ? 'bg-[#8a2be2] text-white hover:bg-[#8a2be2]/90'
                : 'bg-[#262131] border-[#3b3b3b] text-[#94a3b8] hover:bg-[#262131]/80 hover:text-white'
            }`}
            onClick={() => onPlanChange('contract')}
          >
            Contract
          </Button>
        </div>

        {/* Plan Summary */}
        <Card className="bg-[#262131] border-[#3b3b3b]">
          <CardHeader>
            <h3 className="text-white font-semibold">Plan Summary:</h3>
          </CardHeader>
          <CardContent className="space-y-3">
            {isContract ? (
              <>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-[#94a3b8] text-sm">Fixed payment frequency</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-[#94a3b8] text-sm">Guaranteed device availability</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-[#94a3b8] text-sm">No per-task accounting</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-[#94a3b8] text-sm">Common for enterprise use</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-[#94a3b8] text-sm">No transaction fees</span>
                </div>
              </>
            ) : (
              <>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-[#94a3b8] text-sm">Earnings are usage-based</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-[#94a3b8] text-sm">
                    Automatic credit-to-cash conversion
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-[#94a3b8] text-sm">Payout frequency varies</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-[#94a3b8] text-sm">Real-time usage tracking</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-[#94a3b8] text-sm">Flexible and scalable</span>
                </div>
              </>
            )}

            <Button
              variant="outline"
              className="w-full mt-4 bg-transparent border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white"
            >
              See Full Description
            </Button>
          </CardContent>
        </Card>

        {/* Contract-specific content */}
        {isContract && (
          <Card className="bg-[#262131] border-[#3b3b3b]">
            <CardHeader>
              <h3 className="text-white font-semibold">Your Agreement With qBraid:</h3>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#94a3b8] text-sm">qBraid will pay upfront for access</span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#94a3b8] text-sm">
                  qBraid has access to the device for a certain period of time
                </span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#94a3b8] text-sm">
                  There will be a set start and end date for access
                </span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#94a3b8] text-sm">
                  Contracts can be negotiated to provide unlimited access
                </span>
              </div>

              <div className="mt-6">
                <h4 className="text-white font-medium mb-3">Electronic Signature(s):</h4>
                <div className="relative">
                  <Textarea
                    className="bg-[#1d1825] border-[#3b3b3b] text-white placeholder:text-[#94a3b8] min-h-[100px] resize-none"
                    placeholder="Enter your electronic signature here..."
                  />
                  <Button
                    size="sm"
                    className="absolute bottom-3 right-3 bg-[#8a2be2] hover:bg-[#8a2be2]/90 text-white rounded-full w-8 h-8 p-0"
                  >
                    <Check className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Right Side - Document/Form */}
      <div>
        {isContract ? (
          <Card className="bg-[#262131] border-[#3b3b3b] h-full">
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex space-x-2">
                <Button size="sm" variant="ghost" className="text-[#94a3b8] hover:text-white p-2">
                  <ExternalLink className="w-4 h-4" />
                </Button>
                <Button size="sm" variant="ghost" className="text-[#94a3b8] hover:text-white p-2">
                  <Download className="w-4 h-4" />
                </Button>
                <Button size="sm" variant="ghost" className="text-[#94a3b8] hover:text-white p-2">
                  <Edit className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-white rounded-lg p-6 h-[600px] overflow-y-auto">
                <div className="text-black space-y-4">
                  <div className="flex justify-between items-start mb-6">
                    <div className="text-sm">
                      <p>State of _______</p>
                    </div>
                    <div className="text-sm">
                      <p>Rev. 13/04/24</p>
                    </div>
                  </div>

                  <div className="text-center mb-8">
                    <h1 className="text-xl font-bold">PARTNERSHIP AGREEMENT</h1>
                  </div>

                  <div className="space-y-4 text-sm leading-relaxed">
                    <p>
                      This Partnership Agreement (the "Agreement") is made as of the _____ day of
                      _______, 20__ (the "Effective Date") by and between _________________, located
                      at _________________ and _________________, located at _________________
                      (each, a "Partner" and collectively, the "Partners").
                    </p>

                    <div className="space-y-3">
                      <p>
                        <strong>1. Partnership Formation.</strong> The Partners agree to form a
                        partnership under the name _________________ (the "Partnership"). The
                        Partnership will be governed in accordance with the laws of the State of
                        _______. The Partnership has been formed on the terms and conditions set
                        forth below to engage in the business of _________________ and to engage in
                        any and all other activities as may be necessary, related or incidental to
                        carry on the business of the Partnership as provided herein.
                      </p>

                      <p>
                        <strong>2. Place of Business.</strong> The principal office of the
                        Partnership will be located at _________________ or at such places as the
                        Partners shall determine from time to time.
                      </p>

                      <p>
                        <strong>3. Partnership Term.</strong> The Partnership shall commence on the
                        Effective Date and will continue until it is terminated in accordance with
                        the terms of this Agreement, unless terminated earlier in accordance with
                        the terms of this Agreement.
                      </p>

                      <p>
                        <strong>4. Partners' Capital Contributions.</strong> The Partners will
                        contribute capital to the Partnership.
                      </p>

                      <p>The Partners' cash contribution will be: _________________</p>
                      <p>
                        The Partners' non-cash contribution and the value of the non-cash
                        contribution will be: _________________
                      </p>

                      <p>
                        <strong>5. Partners' Capital Accounts.</strong> The Partnership will
                        establish and maintain for each Partner a separate capital account
                        consisting of the Partner's capital contributions. A Partner may not
                        withdraw any portion of capital from his or her capital account without the
                        written consent of all Partners. Interest, at the rates and terms determined
                        by the Partners, may be paid on capital account balances.
                      </p>

                      <p>
                        <strong>6. Profits and Losses.</strong> The net profits and losses of the
                        Partnership will be divided according to the following percentages:
                      </p>

                      <p>
                        <strong>7. Partner's Income Accounts.</strong> The Partnership will
                        establish and maintain a separate income account for each Partner. Each
                        Partner's share of the Partnership profits and losses will be credited to or
                        charged against his or her income account. If there is no positive balance
                        in a Partner's income account, losses will be charged against the Partner's
                        capital account.
                      </p>

                      <p>
                        <strong>8. Partners' Salary and Drawings.</strong> Any salaries will not be
                        charged against the Partners' capital accounts or the Partners' income
                        accounts. The Partnership will distribute profits to Partners at the end of
                        each year or at the times and in the amounts as determined by the Partners.
                      </p>
                    </div>

                    <div className="mt-8 text-center">
                      <p className="text-sm">Partnership Agreement (Rev. 13/04/24)</p>
                      <p className="text-sm font-medium">1 / 4</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="bg-[#262131] border-[#3b3b3b]">
            <CardHeader>
              <h3 className="text-white text-xl font-semibold">Bank Information Form</h3>
              <p className="text-[#94a3b8] text-sm">
                Enter the details below to connect a bank account.
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="bank-name" className="text-[#94a3b8] text-sm">
                  Bank Name
                </Label>
                <Input
                  id="bank-name"
                  className="bg-[#bab2c7] border-none text-black placeholder:text-gray-500 mt-1"
                  placeholder="Enter bank name"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="first-name" className="text-[#94a3b8] text-sm">
                    First / Last Name
                  </Label>
                  <Input
                    id="first-name"
                    className="bg-[#bab2c7] border-none text-black placeholder:text-gray-500 mt-1"
                    placeholder="First"
                  />
                </div>
                <div>
                  <Label htmlFor="last-name" className="text-[#94a3b8] text-sm sr-only">
                    Last Name
                  </Label>
                  <Input
                    id="last-name"
                    className="bg-[#bab2c7] border-none text-black placeholder:text-gray-500 mt-6"
                    placeholder="Last"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="branch-number" className="text-[#94a3b8] text-sm">
                    Branch Number
                  </Label>
                  <Input
                    id="branch-number"
                    className="bg-[#bab2c7] border-none text-black placeholder:text-gray-500 mt-1"
                    placeholder="Enter branch number"
                  />
                </div>
                <div>
                  <Label htmlFor="branch-name" className="text-[#94a3b8] text-sm">
                    Branch Name
                  </Label>
                  <Input
                    id="branch-name"
                    className="bg-[#bab2c7] border-none text-black placeholder:text-gray-500 mt-1"
                    placeholder="Enter branch name"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="branch-address" className="text-[#94a3b8] text-sm">
                  Branch Address
                </Label>
                <Input
                  id="branch-address"
                  className="bg-[#bab2c7] border-none text-black placeholder:text-gray-500 mt-1"
                  placeholder="Street Address"
                />
                <Input
                  className="bg-[#bab2c7] border-none text-black placeholder:text-gray-500 mt-2"
                  placeholder="Street Address Line 2"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Input
                    className="bg-[#bab2c7] border-none text-black placeholder:text-gray-500"
                    placeholder="City"
                  />
                </div>
                <div>
                  <Input
                    className="bg-[#bab2c7] border-none text-black placeholder:text-gray-500"
                    placeholder="Region"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Input
                    className="bg-[#bab2c7] border-none text-black placeholder:text-gray-500"
                    placeholder="Postal / Zip Code"
                  />
                </div>
                <div>
                  <Select>
                    <SelectTrigger className="bg-[#bab2c7] border-none text-black">
                      <SelectValue placeholder="Country" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="us">United States</SelectItem>
                      <SelectItem value="ca">Canada</SelectItem>
                      <SelectItem value="uk">United Kingdom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="currency" className="text-[#94a3b8] text-sm">
                  Bank Account Currency
                </Label>
                <Input
                  id="currency"
                  className="bg-[#bab2c7] border-none text-black placeholder:text-gray-500 mt-1"
                  placeholder="Enter currency"
                />
              </div>

              <div>
                <Label className="text-[#94a3b8] text-sm">Account Type</Label>
                <RadioGroup defaultValue="checking" className="mt-2">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem
                      value="checking"
                      id="checking"
                      className="border-[#94a3b8] text-[#8a2be2]"
                    />
                    <Label htmlFor="checking" className="text-[#94a3b8] text-sm">
                      Checking
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem
                      value="savings"
                      id="savings"
                      className="border-[#94a3b8] text-[#8a2be2]"
                    />
                    <Label htmlFor="savings" className="text-[#94a3b8] text-sm">
                      Savings
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="flex justify-end pt-4">
                <Button className="bg-[#8a2be2] hover:bg-[#8a2be2]/90 text-white px-8">
                  Submit
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

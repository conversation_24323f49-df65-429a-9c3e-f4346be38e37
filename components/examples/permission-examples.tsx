'use client';

import { usePermissions, PermissionGuard } from '@/hooks/use-permissions';
import { Permission } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

/**
 * Example component showing different ways to use the RBAC system with QBraid roles
 */
export function PermissionExamples() {
  const {
    permissions,
    roles,
    hasPermission,
    hasAnyPermission,
    isAdmin,
    loading,
    refreshPermissions,
  } = usePermissions();

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-4 bg-gray-200 rounded w-3/4" />
        <div className="h-4 bg-gray-200 rounded w-1/2" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current User Permissions Display */}
      <Card>
        <CardHeader>
          <CardTitle>Your Current Access</CardTitle>
          <CardDescription>Your roles and permissions from the QBraid API</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Roles:</h4>
            <div className="flex flex-wrap gap-2">
              {roles.length > 0 ? (
                roles.map((role) => (
                  <Badge key={role} variant="secondary">
                    {role}
                  </Badge>
                ))
              ) : (
                <Badge variant="outline">No roles assigned</Badge>
              )}
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">Permissions:</h4>
            <div className="flex flex-wrap gap-2">
              {permissions.length > 0 ? (
                permissions.map((permission) => (
                  <Badge key={permission} variant="outline">
                    {permission}
                  </Badge>
                ))
              ) : (
                <Badge variant="outline">No permissions granted</Badge>
              )}
            </div>
          </div>

          <Button onClick={refreshPermissions} variant="outline" size="sm">
            Refresh Permissions from QBraid API
          </Button>
        </CardContent>
      </Card>

      {/* Conditional Rendering Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Conditional Rendering Examples</CardTitle>
          <CardDescription>
            Different ways to show/hide content based on permissions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Simple permission check */}
          {hasPermission(Permission.ViewDevices) ? (
            <Alert>
              <AlertDescription>✅ You can view devices</AlertDescription>
            </Alert>
          ) : (
            <Alert variant="destructive">
              <AlertDescription>❌ You cannot view devices</AlertDescription>
            </Alert>
          )}

          {/* Multiple permission check */}
          {hasAnyPermission([Permission.ManageDevices, Permission.AdminAccess]) && (
            <Alert>
              <AlertDescription>✅ You can manage devices or have admin access</AlertDescription>
            </Alert>
          )}

          {/* Admin check */}
          {isAdmin() && (
            <Alert>
              <AlertDescription>✅ You are an administrator</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Permission Guard Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Permission Guard Examples</CardTitle>
          <CardDescription>
            Using the PermissionGuard component for declarative access control
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Single permission guard */}
          <PermissionGuard
            permission={Permission.ViewDevices}
            fallback={
              <p className="text-gray-500">You need device viewing permission to see this.</p>
            }
          >
            <Button className="w-full">View All Devices</Button>
          </PermissionGuard>

          {/* Multiple permissions with OR logic */}
          <PermissionGuard
            permissions={[Permission.ManageDevices, Permission.AdminAccess]}
            requireAll={false}
            fallback={
              <p className="text-gray-500">You need device management or admin permission.</p>
            }
          >
            <Button variant="destructive" className="w-full">
              Delete Device
            </Button>
          </PermissionGuard>

          {/* Multiple permissions with AND logic */}
          <PermissionGuard
            permissions={[Permission.ViewTeam, Permission.ManageTeam]}
            requireAll={true}
            fallback={
              <p className="text-gray-500">
                You need both team viewing AND management permissions.
              </p>
            }
          >
            <Button variant="outline" className="w-full">
              Advanced Team Management
            </Button>
          </PermissionGuard>

          {/* Role-based guard for QBraid admin */}
          <PermissionGuard
            role="qbraid_admin"
            fallback={<p className="text-gray-500">Only QBraid admins can see this.</p>}
          >
            <Button variant="secondary" className="w-full">
              QBraid Admin Panel
            </Button>
          </PermissionGuard>

          {/* Role-based guard for device manager */}
          <PermissionGuard
            role="device_manager"
            fallback={<p className="text-gray-500">Only device managers can see this.</p>}
          >
            <Button variant="outline" className="w-full">
              Device Manager Tools
            </Button>
          </PermissionGuard>
        </CardContent>
      </Card>

      {/* Real-world UI Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Real-world UI Examples</CardTitle>
          <CardDescription>
            Practical examples of how to use permissions in your quantum device dashboard
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DeviceManagementExample />
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Example of a realistic device management component with QBraid permissions
 */
function DeviceManagementExample() {
  const { hasPermission, hasRole } = usePermissions();

  const devices = [
    { id: 1, name: 'Quantum Device 1', status: 'Online', type: 'Superconducting' },
    { id: 2, name: 'Quantum Device 2', status: 'Offline', type: 'Trapped Ion' },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Quantum Device Management</h3>

        {/* Only device managers and admins can add devices */}
        <PermissionGuard permission={Permission.ManageDevices}>
          <Button size="sm">Add New Device</Button>
        </PermissionGuard>
      </div>

      <div className="grid gap-4">
        {devices.map((device) => (
          <div key={device.id} className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium">{device.name}</h4>
              <p className="text-sm text-gray-600">
                {device.type} • {device.status}
              </p>
            </div>

            <div className="flex gap-2">
              {/* Always show view button if user can view devices */}
              <PermissionGuard permission={Permission.ViewDevices}>
                <Button size="sm" variant="outline">
                  View Details
                </Button>
              </PermissionGuard>

              {/* Show edit button for device managers */}
              <PermissionGuard permission={Permission.ManageDevices}>
                <Button size="sm">Configure</Button>
              </PermissionGuard>

              {/* Show advanced controls only for admins */}
              <PermissionGuard permission={Permission.AdminAccess}>
                <Button size="sm" variant="destructive">
                  Delete
                </Button>
              </PermissionGuard>
            </div>
          </div>
        ))}
      </div>

      {/* QBraid Admin-only section */}
      <PermissionGuard
        permission={Permission.AdminAccess}
        fallback={
          <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
            QBraid Admin controls are hidden for non-administrators
          </div>
        }
      >
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <h4 className="font-medium text-red-800 mb-2">QBraid Admin Controls</h4>
          <div className="flex gap-2">
            <Button size="sm" variant="destructive">
              Reset All Devices
            </Button>
            <Button size="sm" variant="outline">
              System Maintenance
            </Button>
            <Button size="sm" variant="outline">
              Global Configuration
            </Button>
          </div>
        </div>
      </PermissionGuard>

      {/* Device Manager specific tools */}
      <PermissionGuard role="device_manager" fallback={null}>
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">Device Manager Tools</h4>
          <div className="flex gap-2">
            <Button size="sm" variant="outline">
              Bulk Configure
            </Button>
            <Button size="sm" variant="outline">
              Performance Analytics
            </Button>
            <Button size="sm" variant="outline">
              Calibration Schedule
            </Button>
          </div>
        </div>
      </PermissionGuard>
    </div>
  );
}

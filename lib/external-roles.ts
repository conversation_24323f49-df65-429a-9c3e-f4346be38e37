'use server';

import { ExternalRoleResponse } from '@/types/auth';
import { getRedisClient, isRedisHealthy } from './redis';

// Cache configuration
const ROLES_CACHE_PREFIX = 'roles:';
const ROLES_CACHE_TTL = 3600; // 1 hour in seconds

/**
 * Organization structure from QBraid API
 */
interface Organization {
  org: {
    role: string;
    email: string;
    organization: {
      name: string;
      _id: string;
    };
  };
}

interface OrganizationsResponse {
  organizations: Organization[];
  pagination: {
    currentPage: number;
    limit: string;
    totalPages: number;
    totalOrganizations: number;
  };
}

/**
 * Server-side API client for QBraid organizations
 * This works with the mock endpoint until real API is connected
 */
async function fetchFromOrganizationsAPI(endpoint: string): Promise<any> {
  try {
    // For now, we'll use the internal Next.js API route
    // This calls the mock endpoint that returns sample data
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const url = `${baseUrl}/api${endpoint}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add any necessary headers for internal API calls
      },
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  } catch (error) {
    console.error(`❌ [API-CLIENT] Failed to fetch from ${endpoint}:`, error);
    throw error;
  }
}

/**
 * Fetch roles from QBraid organizations API using the existing external client
 */
export async function fetchRolesFromExternalApi(email: string): Promise<string[]> {
  // Use the organizations endpoint to get user roles
  const orgsEndpoint = '/orgs/get/0/50'; // Get first 50 organizations to ensure we capture user's roles

  try {
    console.log(`🔍 [EXTERNAL-ROLES] Fetching roles for email: ${email} from organizations`);

    // Use the server-side API client to call our mock organizations endpoint
    const data = await fetchFromOrganizationsAPI(orgsEndpoint);

    // Handle response data based on QBraid API format
    const organizations = data.organizations || [];

    // Extract roles from organizations where the user's email matches (case-insensitive)
    const userRoles: string[] = organizations
      .filter((org: any) => org.org?.email?.toLowerCase() === email.toLowerCase())
      .map((org: any) => org.org?.role)
      .filter((role: any): role is string => typeof role === 'string' && role.length > 0);

    // Remove duplicates and ensure we have valid roles
    const uniqueRoles = [...new Set(userRoles)];

    console.log(
      `✅ [EXTERNAL-ROLES] Retrieved ${uniqueRoles.length} roles for ${email}:`,
      uniqueRoles,
    );
    console.log(`🏢 [EXTERNAL-ROLES] Found user in ${organizations.length} total organizations`);

    return uniqueRoles;
  } catch (error: any) {
    console.error(`❌ [EXTERNAL-ROLES] Failed to fetch roles for ${email}:`, error);

    // Log more specific error information if available
    if (error.status) {
      console.error(`❌ [EXTERNAL-ROLES] API Error ${error.status}:`, error.message);
    }

    // Return empty array as fallback to allow user to continue with minimal permissions
    return [];
  }
}

/**
 * Get cached roles from Redis
 */
async function getCachedRoles(email: string): Promise<string[] | null> {
  if (!(await isRedisHealthy())) {
    return null;
  }

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email}`;
    const cached = await redis.get(cacheKey);

    if (cached) {
      const roles = JSON.parse(cached);
      console.log(`💾 [EXTERNAL-ROLES] Retrieved cached roles for ${email}:`, roles);
      return roles;
    }

    return null;
  } catch (error) {
    console.error(`❌ [EXTERNAL-ROLES] Failed to get cached roles for ${email}:`, error);
    return null;
  }
}

/**
 * Cache roles in Redis
 */
async function cacheRoles(
  email: string,
  roles: string[],
  ttl: number = ROLES_CACHE_TTL,
): Promise<void> {
  if (!(await isRedisHealthy())) {
    return;
  }

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email}`;

    await redis.setex(cacheKey, ttl, JSON.stringify(roles));
    console.log(`💾 [EXTERNAL-ROLES] Cached ${roles.length} roles for ${email} (TTL: ${ttl}s)`);
  } catch (error) {
    console.error(`❌ [EXTERNAL-ROLES] Failed to cache roles for ${email}:`, error);
  }
}

/**
 * Get roles with caching - main function to use
 */
export async function getRolesForUser(
  email: string,
  forceRefresh: boolean = false,
): Promise<string[]> {
  // Check cache first unless force refresh is requested
  if (!forceRefresh) {
    const cachedRoles = await getCachedRoles(email);
    if (cachedRoles !== null) {
      return cachedRoles;
    }
  }

  // Fetch from external API
  const roles = await fetchRolesFromExternalApi(email);

  // Cache the result (even if empty to prevent repeated API calls for non-existent users)
  await cacheRoles(email, roles);

  return roles;
}

/**
 * Invalidate cached roles for a user (useful for webhook updates)
 */
export async function invalidateUserRoles(email: string): Promise<void> {
  if (!(await isRedisHealthy())) {
    return;
  }

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email}`;

    await redis.del(cacheKey);
    console.log(`🗑️ [EXTERNAL-ROLES] Invalidated cached roles for ${email}`);
  } catch (error) {
    console.error(`❌ [EXTERNAL-ROLES] Failed to invalidate roles for ${email}:`, error);
  }
}

/**
 * Update cached roles for a user (useful for webhook updates)
 */
export async function updateUserRoles(email: string, roles: string[]): Promise<void> {
  await cacheRoles(email, roles);
  console.log(`🔄 [EXTERNAL-ROLES] Updated cached roles for ${email}:`, roles);
}

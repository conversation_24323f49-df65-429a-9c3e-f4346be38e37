'use server';

import { ExternalRoleResponse } from '@/types/auth';
import { getRedisClient, isRedisHealthy } from './redis';

// Cache configuration
const ROLES_CACHE_PREFIX = 'roles:';
const ROLES_CACHE_TTL = 3600; // 1 hour in seconds

/**
 * Fetch roles from QBraid API using the existing external client
 */
export async function fetchRolesFromExternalApi(email: string): Promise<string[]> {
  // Import the external client dynamically to avoid circular dependencies
  const { externalClient } = await import('@/app/api/_utils/external-client');
  const rolesEndpoint = process.env.EXTERNAL_ROLES_ENDPOINT || '/user/roles';

  try {
    console.log(`🔍 [EXTERNAL-ROLES] Fetching roles for email: ${email}`);

    // Use the existing external client which handles auth automatically
    const response = await externalClient.get(rolesEndpoint, { email });

    // Handle response data based on your API's format
    const data = response.data as ExternalRoleResponse;
    const roles = data.roles || [];

    console.log(`✅ [EXTERNAL-ROLES] Retrieved ${roles.length} roles for ${email}:`, roles);
    return roles;
  } catch (error: any) {
    console.error(`❌ [EXTERNAL-ROLES] Failed to fetch roles for ${email}:`, error);

    // Log more specific error information if available
    if (error.status) {
      console.error(`❌ [EXTERNAL-ROLES] API Error ${error.status}:`, error.message);
    }

    // Return empty array as fallback to allow user to continue with minimal permissions
    return [];
  }
}

/**
 * Get cached roles from Redis
 */
async function getCachedRoles(email: string): Promise<string[] | null> {
  if (!(await isRedisHealthy())) {
    return null;
  }

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email}`;
    const cached = await redis.get(cacheKey);

    if (cached) {
      const roles = JSON.parse(cached);
      console.log(`💾 [EXTERNAL-ROLES] Retrieved cached roles for ${email}:`, roles);
      return roles;
    }

    return null;
  } catch (error) {
    console.error(`❌ [EXTERNAL-ROLES] Failed to get cached roles for ${email}:`, error);
    return null;
  }
}

/**
 * Cache roles in Redis
 */
async function cacheRoles(
  email: string,
  roles: string[],
  ttl: number = ROLES_CACHE_TTL,
): Promise<void> {
  if (!(await isRedisHealthy())) {
    return;
  }

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email}`;

    await redis.setex(cacheKey, ttl, JSON.stringify(roles));
    console.log(`💾 [EXTERNAL-ROLES] Cached ${roles.length} roles for ${email} (TTL: ${ttl}s)`);
  } catch (error) {
    console.error(`❌ [EXTERNAL-ROLES] Failed to cache roles for ${email}:`, error);
  }
}

/**
 * Get roles with caching - main function to use
 */
export async function getRolesForUser(
  email: string,
  forceRefresh: boolean = false,
): Promise<string[]> {
  // Check cache first unless force refresh is requested
  if (!forceRefresh) {
    const cachedRoles = await getCachedRoles(email);
    if (cachedRoles !== null) {
      return cachedRoles;
    }
  }

  // Fetch from external API
  const roles = await fetchRolesFromExternalApi(email);

  // Cache the result (even if empty to prevent repeated API calls for non-existent users)
  await cacheRoles(email, roles);

  return roles;
}

/**
 * Invalidate cached roles for a user (useful for webhook updates)
 */
export async function invalidateUserRoles(email: string): Promise<void> {
  if (!(await isRedisHealthy())) {
    return;
  }

  try {
    const redis = getRedisClient();
    const cacheKey = `${ROLES_CACHE_PREFIX}${email}`;

    await redis.del(cacheKey);
    console.log(`🗑️ [EXTERNAL-ROLES] Invalidated cached roles for ${email}`);
  } catch (error) {
    console.error(`❌ [EXTERNAL-ROLES] Failed to invalidate roles for ${email}:`, error);
  }
}

/**
 * Update cached roles for a user (useful for webhook updates)
 */
export async function updateUserRoles(email: string, roles: string[]): Promise<void> {
  await cacheRoles(email, roles);
  console.log(`🔄 [EXTERNAL-ROLES] Updated cached roles for ${email}:`, roles);
}
